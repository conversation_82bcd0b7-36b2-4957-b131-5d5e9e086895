<?php

namespace App\Http\Controllers;

use App\Models\Chapter;
use App\Models\Story;
use Illuminate\Http\Request;

class ChapterController extends Controller
{
    // Hiển thị danh sách chương theo truyện
    public function index($storyId)
    {
        $story = Story::findOrFail($storyId);
        $chapters = Chapter::where('story_id', $storyId)
            ->orderBy('chapter_number')
            ->paginate(20);

        return view('chapters.index', compact('story', 'chapters'));
    }

    // Hiển thị form tạo chương
    public function create($storyId = null)
    {
        if ($storyId) {
            $story = Story::findOrFail($storyId);
            return view('chapters.create', compact('story'));
        }
        
        $stories = Story::all();
        return view('chapters.create', compact('stories'));
    }

    // Lưu chương mới
    public function store(Request $request)
    {
        $validated = $request->validate([
            'story_id' => 'required|exists:stories,id',
            'chapter_number' => 'required|integer',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $chapter = Chapter::create($validated);

        return redirect()->route('chapters.index', $validated['story_id'])
            ->with('success', 'Đã thêm chương mới.');
    }

    // Hiển thị form sửa chương
    public function edit($id)
    {
        $chapter = Chapter::findOrFail($id);
        return view('chapters.edit', compact('chapter'));
    }

    // Cập nhật chương
    public function update(Request $request, $id)
    {
        $chapter = Chapter::findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        $chapter->update($validated);

        return redirect()->route('chapters.index', $chapter->story_id)
            ->with('success', 'Cập nhật chương thành công.');
    }

    // Xóa chương
    public function destroy($id)
    {
        $chapter = Chapter::findOrFail($id);
        $chapter->delete();

        return back()->with('success', 'Đã xóa chương.');
    }

    // ✅ API lưu chương từ crawl.js
    public function storeFromCrawler(Request $request)
    {
        $validated = $request->validate([
            'story_id' => 'required|exists:stories,id',
            'chapter_number' => 'required|integer',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);

        Chapter::updateOrCreate(
            [
                'story_id' => $validated['story_id'],
                'chapter_number' => $validated['chapter_number'],
            ],
            [
                'title' => $validated['title'],
                'content' => $validated['content'],
                'is_crawled' => true,
                'crawled_at' => now(),
            ]
        );

        return response()->json(['message' => 'Chapter saved'], 200);
    }

    // Xem chi tiết một chương (nếu cần)
    public function show($id)
    {
        $chapter = Chapter::findOrFail($id);
        return view('chapters.show', compact('chapter'));
    }
}
