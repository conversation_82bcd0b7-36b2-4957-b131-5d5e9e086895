<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Sửa chương <?php echo e($chapter->chapter_number); ?> - <?php echo e($chapter->story->title); ?></h3>
            <div class="card-tools">
                <a href="<?php echo e(route('stories.chapters', $chapter->story)); ?>" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if(session('success')): ?>
                <div class="alert alert-success"><?php echo e(session('success')); ?></div>
            <?php endif; ?>
            
            <?php if(session('error')): ?>
                <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
            <?php endif; ?>
            
            <!-- Thông tin nguồn dữ liệu -->
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> Thông tin nguồn dữ liệu:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Trạng thái:</strong> 
                        <?php if($chapter->is_crawled): ?>
                            <span class="badge badge-success">Đã crawl</span>
                        <?php else: ?>
                            <span class="badge badge-secondary">Thêm thủ công</span>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Nguồn nội dung:</strong>
                        <?php if($chapter->hasContentInDatabase()): ?>
                            <span class="badge badge-primary">Database</span>
                        <?php endif; ?>
                        <?php if($chapter->file_path): ?>
                            <span class="badge badge-info">File</span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php if($chapter->file_path): ?>
                    <div class="mt-2">
                        <strong>File:</strong> <code><?php echo e($chapter->file_path); ?></code>
                        <?php if($chapter->formatted_file_size): ?>
                            (<?php echo e($chapter->formatted_file_size); ?>)
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <form action="<?php echo e(route('chapters.update', $chapter)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="chapter_number">Số chương</label>
                            <input type="number" name="chapter_number" id="chapter_number" class="form-control" 
                                   value="<?php echo e(old('chapter_number', $chapter->chapter_number)); ?>" required min="1">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="title">Tiêu đề</label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   value="<?php echo e(old('title', $chapter->title)); ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="content">Nội dung</label>
                    <?php if(!$chapter->hasContentInDatabase() && $chapter->file_path): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            Nội dung hiện đang được đọc từ file. Nếu bạn sửa và lưu, nội dung sẽ được lưu vào database.
                        </div>
                    <?php endif; ?>
                    <textarea name="content" id="content" class="form-control" rows="20" required><?php echo e(old('content', $chapter->content)); ?></textarea>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" name="is_crawled" value="1" class="form-check-input" id="is_crawled"
                               <?php echo e(old('is_crawled', $chapter->is_crawled) ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="is_crawled">
                            Đánh dấu là đã crawl
                        </label>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <a href="<?php echo e(route('stories.chapters', $chapter->story)); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Hủy
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\audio-lara\resources\views/chapters/edit.blade.php ENDPATH**/ ?>