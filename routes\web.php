<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StoryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ChapterController;
use App\Http\Controllers\GenreController;
use App\Http\Controllers\CrawlController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::resource('/',DashboardController::class);
Route::resource('stories', StoryController::class);
Route::resource('chapters', ChapterController::class);
Route::resource('genres', GenreController::class);
Route::get('/crawl', [\App\Http\Controllers\CrawlController::class, 'index'])->name('crawl.index');
Route::post('/crawl/run/{story}', [\App\Http\Controllers\CrawlController::class, 'run'])->name('crawl.run');

// Story management routes
Route::resource('stories', StoryController::class);

// Story chapters management
Route::get('/stories/{story}/chapters', [StoryController::class, 'chapters'])->name('stories.chapters');

// Story crawling
Route::get('/stories/{story}/crawl', [StoryController::class, 'showCrawlForm'])->name('stories.crawl.form');
Route::post('/stories/{story}/crawl', [StoryController::class, 'crawl'])->name('stories.crawl');

// Story text-to-speech
Route::get('/stories/{story}/tts', [StoryController::class, 'showTtsForm'])->name('stories.tts.form');
Route::post('/stories/{story}/tts', [StoryController::class, 'tts'])->name('stories.tts');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');
// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');
// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');
// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');
// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');

// Text-to-Speech routes
Route::get('/tts', [App\Http\Controllers\TextToSpeechController::class, 'index'])->name('tts.index');
Route::post('/tts/convert', [App\Http\Controllers\TextToSpeechController::class, 'convert'])->name('tts.convert');



