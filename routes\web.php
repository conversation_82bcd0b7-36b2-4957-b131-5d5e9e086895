<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\StoryController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ChapterController;
use App\Http\Controllers\Admin\GenreController;
use App\Http\Controllers\CrawlController;
use App\Http\Controllers\TextToSpeechController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Auth\LoginController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Redirect root to admin dashboard
Route::get('/', function () {
    return redirect()->route('admin.dashboard');
});

// Protected routes (require authentication)
Route::middleware(['auth'])->group(function () {
    // Admin routes with /admin prefix
    Route::prefix('admin')->name('admin.')->group(function () {
        // Dashboard
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

        // Stories management
        Route::resource('stories', StoryController::class);

        // Story chapters management
        Route::get('/stories/{story}/chapters', [StoryController::class, 'chapters'])->name('stories.chapters');

        // Story crawling
        Route::get('/stories/{story}/crawl', [StoryController::class, 'showCrawlForm'])->name('stories.crawl.form');
        Route::post('/stories/{story}/crawl', [StoryController::class, 'crawl'])->name('stories.crawl');

        // Story text-to-speech
        Route::get('/stories/{story}/tts', [StoryController::class, 'showTtsForm'])->name('stories.tts.form');
        Route::post('/stories/{story}/tts', [StoryController::class, 'tts'])->name('stories.tts');

        // Story chapter scanning
        Route::get('/stories/{story}/scan', [StoryController::class, 'showScanForm'])->name('stories.scan.form');
        Route::post('/stories/{story}/scan', [StoryController::class, 'scanChapters'])->name('stories.scan');

        // Story video generation
        Route::get('/stories/{story}/video', [StoryController::class, 'showVideoForm'])->name('stories.video');
        Route::post('/stories/{story}/video', [StoryController::class, 'generateVideo'])->name('stories.video.generate');

        // Video overlay management
        Route::post('/overlay/upload', [StoryController::class, 'uploadOverlay'])->name('overlay.upload');
        Route::delete('/overlay/delete', [StoryController::class, 'deleteOverlay'])->name('overlay.delete');

        // Chapters management
        Route::resource('chapters', ChapterController::class);
        Route::get('/chapters/create/{story_id?}', [ChapterController::class, 'create'])->name('chapters.create');
        Route::get('/chapters/story/{story_id}', [ChapterController::class, 'index'])->name('chapters.index');

        // Chapter TTS routes
        Route::post('/chapters/{chapter}/tts', [ChapterController::class, 'convertToTts'])->name('chapters.tts');
        Route::post('/stories/{story}/chapters/tts-all', [ChapterController::class, 'convertAllToTts'])->name('chapters.tts.all');

        // Chapter content route
        Route::get('/chapters/{chapter}/content', [ChapterController::class, 'getContent'])->name('chapters.content');

        // Genres management
        Route::resource('genres', GenreController::class);

        // User management (Admin only)
        Route::middleware(['admin'])->group(function () {
            Route::resource('users', UserController::class);
        });

        // Legacy crawl routes
        Route::get('/crawl', [CrawlController::class, 'index'])->name('crawl.index');
        Route::post('/crawl/run/{story}', [CrawlController::class, 'run'])->name('crawl.run');
    });

    // Audio file serving route (outside admin prefix for direct access)
    Route::get('/audio/{path}', function ($path) {
        $audioBasePath = config('constants.STORAGE_PATHS.AUDIO');
        $fullPath = base_path($audioBasePath . $path);

        if (!file_exists($fullPath)) {
            abort(404);
        }

        return response()->file($fullPath);
    })->where('path', '.*');
});
