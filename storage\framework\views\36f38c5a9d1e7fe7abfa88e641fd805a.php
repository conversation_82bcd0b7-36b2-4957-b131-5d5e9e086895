<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🎬 Tạo Video - <?php echo e($story->title); ?></h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('stories.show', $story)); ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php endif; ?>
                    
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
                    <?php endif; ?>
                    
                    <?php if(!$hasImage): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>Cảnh báo:</strong> Truyện chưa có ảnh nền. Vui lòng upload ảnh cho truyện trước khi tạo video.
                        </div>
                    <?php endif; ?>
                    
                    <?php if(empty($audioFiles)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>Cảnh báo:</strong> Không tìm thấy file audio MP3. Vui lòng tạo audio bằng TTS trước.
                        </div>
                    <?php endif; ?>
                    
                    <?php if(empty($overlayFiles)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 
                            <strong>Thông tin:</strong> Không tìm thấy file overlay video. Hệ thống sẽ sử dụng file mặc định.
                        </div>
                    <?php endif; ?>
                    
                    <form action="<?php echo e(route('stories.video.generate', $story)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="form-group">
                            <label for="chapter_number">Chương cần tạo video</label>
                            <select name="chapter_number" id="chapter_number" class="form-control">
                                <option value="">Tạo video tổng hợp (sử dụng audio đầu tiên)</option>
                                <?php $__currentLoopData = $audioFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audioFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        preg_match('/chuong[_-](\d+)\.mp3/i', $audioFile, $matches);
                                        $chapterNum = isset($matches[1]) ? $matches[1] : null;
                                    ?>
                                    <?php if($chapterNum): ?>
                                        <option value="<?php echo e($chapterNum); ?>">Chương <?php echo e($chapterNum); ?> (<?php echo e($audioFile); ?>)</option>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="form-text text-muted">
                                Chọn chương cụ thể hoặc để trống để tạo video tổng hợp
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="overlay_file">Video overlay (hiển thị ở góc phải dưới)</label>
                            <select name="overlay_file" id="overlay_file" class="form-control">
                                <option value="">Sử dụng file mặc định</option>
                                <?php $__currentLoopData = $overlayFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $overlayFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($overlayFile); ?>"><?php echo e($overlayFile); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="form-text text-muted">
                                Video ngắn sẽ được lặp lại để khớp với thời lượng audio
                            </small>

                            <!-- Upload overlay video -->
                            <div class="mt-2">
                                <small class="text-info">
                                    <i class="fas fa-info-circle"></i>
                                    Bạn có thể upload thêm video overlay trong phần "Quản lý Overlay Videos" bên phải
                                </small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="output_name">Tên file output (tùy chọn)</label>
                            <input type="text" name="output_name" id="output_name" class="form-control" 
                                   placeholder="Ví dụ: video-gioi-thieu.mp4">
                            <small class="form-text text-muted">
                                Để trống để hệ thống tự đặt tên theo chương hoặc ID truyện
                            </small>
                        </div>
                        
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary" 
                                    <?php echo e((!$hasImage || empty($audioFiles)) ? 'disabled' : ''); ?>>
                                <i class="fas fa-video"></i> Tạo Video
                            </button>
                        </div>
                    </form>
                    
                    <div class="alert alert-info mt-4">
                        <h5><i class="fas fa-info-circle"></i> Thông tin tạo video:</h5>
                        <ul class="mb-0">
                            <li><strong>Input:</strong> Ảnh nền từ truyện + File audio MP3 + Video overlay</li>
                            <li><strong>Output:</strong> Video MP4 với độ phân giải 1280x720</li>
                            <li><strong>Overlay:</strong> Video nhỏ ở góc phải dưới với bo góc tròn</li>
                            <li><strong>Audio:</strong> Được tăng âm lượng +20dB</li>
                            <li><strong>Thời gian:</strong> Quá trình có thể mất 2-5 phút tùy độ dài audio</li>
                            <li><strong>Lưu trữ:</strong> File sẽ được lưu trong thư mục <code>storage/app/videos/<?php echo e($story->folder_name); ?>/</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Thông tin tài nguyên -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📊 Tài nguyên có sẵn</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="info-box">
                                <span class="info-box-icon <?php echo e($hasImage ? 'bg-success' : 'bg-danger'); ?>">
                                    <i class="fas fa-image"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Ảnh nền</span>
                                    <span class="info-box-number"><?php echo e($hasImage ? 'Có' : 'Chưa có'); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="info-box">
                                <span class="info-box-icon <?php echo e(!empty($audioFiles) ? 'bg-success' : 'bg-warning'); ?>">
                                    <i class="fas fa-volume-up"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">File audio</span>
                                    <span class="info-box-number"><?php echo e(count($audioFiles)); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="info-box">
                                <span class="info-box-icon <?php echo e(!empty($overlayFiles) ? 'bg-info' : 'bg-secondary'); ?>">
                                    <i class="fas fa-film"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Video overlay</span>
                                    <span class="info-box-number"><?php echo e(count($overlayFiles)); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="info-box">
                                <span class="info-box-icon <?php echo e(!empty($existingVideos) ? 'bg-primary' : 'bg-light'); ?>">
                                    <i class="fas fa-video"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Video đã tạo</span>
                                    <span class="info-box-number"><?php echo e(count($existingVideos)); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quản lý overlay videos -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🎬 Quản lý Overlay Videos</h3>
                </div>
                <div class="card-body">
                    <?php if(!empty($overlayFiles)): ?>
                        <div class="list-group" id="overlayList">
                            <?php $__currentLoopData = $overlayFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $overlayFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center" data-filename="<?php echo e($overlayFile); ?>">
                                    <span><?php echo e($overlayFile); ?></span>
                                    <button class="btn btn-sm btn-danger delete-overlay" data-filename="<?php echo e($overlayFile); ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">Chưa có file overlay nào</p>
                    <?php endif; ?>

                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#uploadOverlayModal">
                            <i class="fas fa-upload"></i> Upload Overlay Video
                        </button>
                    </div>
                </div>
            </div>

            <!-- Danh sách video đã tạo -->
            <?php if(!empty($existingVideos)): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">🎥 Video đã tạo</h3>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <?php $__currentLoopData = $existingVideos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $video): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><?php echo e($video); ?></span>
                                <small class="text-muted">
                                    <?php
                                        $videoPath = storage_path('app/videos/' . $story->folder_name . '/' . $video);
                                        $size = file_exists($videoPath) ? filesize($videoPath) : 0;
                                        $sizeFormatted = $size > 0 ? round($size / 1024 / 1024, 1) . ' MB' : 'N/A';
                                    ?>
                                    <?php echo e($sizeFormatted); ?>

                                </small>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal Upload Overlay Video -->
<div class="modal fade" id="uploadOverlayModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Video Overlay</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="uploadOverlayForm" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="form-group">
                        <label for="overlay_video">Chọn file video</label>
                        <input type="file" name="overlay_video" id="overlay_video" class="form-control-file"
                               accept=".mp4,.avi,.mov,.wmv" required>
                        <small class="form-text text-muted">
                            Định dạng hỗ trợ: MP4, AVI, MOV, WMV. Kích thước tối đa: 50MB
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                        <ul class="mb-0">
                            <li>Video overlay nên có độ dài 5-15 giây</li>
                            <li>Độ phân giải khuyến nghị: 320x180 hoặc tỷ lệ 16:9</li>
                            <li>Video sẽ được lặp lại để khớp với thời lượng audio</li>
                            <li>Video sẽ hiển thị ở góc phải dưới với bo góc tròn</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="uploadBtn">
                    <i class="fas fa-upload"></i> Upload
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Upload overlay video
    $('#uploadBtn').click(function() {
        var formData = new FormData($('#uploadOverlayForm')[0]);
        var $btn = $(this);
        var originalText = $btn.html();

        $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang upload...').prop('disabled', true);

        $.ajax({
            url: '<?php echo e(route("overlay.upload")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Thêm file mới vào select
                    $('#overlay_file').append(
                        '<option value="' + response.filename + '">' + response.filename + '</option>'
                    );

                    // Thêm vào danh sách quản lý
                    var listItem = `
                        <div class="list-group-item d-flex justify-content-between align-items-center" data-filename="${response.filename}">
                            <span>${response.filename}</span>
                            <button class="btn btn-sm btn-danger delete-overlay" data-filename="${response.filename}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;

                    if ($('#overlayList .list-group-item').length === 0) {
                        $('#overlayList').html(listItem);
                        $('#overlayList').prev('p.text-muted').hide();
                    } else {
                        $('#overlayList').append(listItem);
                    }

                    // Đóng modal và reset form
                    $('#uploadOverlayModal').modal('hide');
                    $('#uploadOverlayForm')[0].reset();

                    // Hiển thị thông báo
                    showAlert('success', response.message);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr) {
                var message = 'Lỗi khi upload file';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert('danger', message);
            },
            complete: function() {
                $btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Xóa overlay video
    $(document).on('click', '.delete-overlay', function() {
        var filename = $(this).data('filename');
        var $item = $(this).closest('.list-group-item');

        if (confirm('Bạn có chắc muốn xóa file "' + filename + '"?')) {
            $.ajax({
                url: '<?php echo e(route("overlay.delete")); ?>',
                type: 'DELETE',
                data: {
                    filename: filename,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Xóa khỏi select
                        $('#overlay_file option[value="' + filename + '"]').remove();

                        // Xóa khỏi danh sách
                        $item.remove();

                        // Hiển thị thông báo nếu không còn file nào
                        if ($('#overlayList .list-group-item').length === 0) {
                            $('#overlayList').html('<p class="text-muted">Chưa có file overlay nào</p>');
                        }

                        showAlert('success', response.message);
                    } else {
                        showAlert('danger', response.message);
                    }
                },
                error: function(xhr) {
                    var message = 'Lỗi khi xóa file';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    showAlert('danger', message);
                }
            });
        }
    });

    // Hàm hiển thị thông báo
    function showAlert(type, message) {
        var alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        $('.card-body').first().prepend(alertHtml);

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\audio-lara\resources\views/stories/video.blade.php ENDPATH**/ ?>