@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><PERSON>h sách chương - {{ $story->title }}</h3>
            <div class="card-tools">
                <a href="{{ route('stories.show', $story) }}" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                <a href="{{ route('chapters.create', ['story_id' => $story->id]) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Thêm chương mới
                </a>
                <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#ttsAllModal">
                    <i class="fas fa-volume-up"></i> TTS tất cả
                </button>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="card-body border-bottom">
            <form method="GET" action="{{ route('stories.chapters', $story) }}" class="form-inline">
                <label for="audio_status" class="mr-2">Lọc theo trạng thái:</label>
                <select name="audio_status" id="audio_status" class="form-control mr-2" onchange="this.form.submit()">
                    <option value="all" {{ request('audio_status', 'all') == 'all' ? 'selected' : '' }}>
                        Tất cả ({{ $chapters->total() }})
                    </option>
                    <option value="pending" {{ request('audio_status') == 'pending' ? 'selected' : '' }}>
                        Chờ xử lý ({{ $statusCounts['pending'] ?? 0 }})
                    </option>
                    <option value="processing" {{ request('audio_status') == 'processing' ? 'selected' : '' }}>
                        Đang xử lý ({{ $statusCounts['processing'] ?? 0 }})
                    </option>
                    <option value="done" {{ request('audio_status') == 'done' ? 'selected' : '' }}>
                        Hoàn thành ({{ $statusCounts['done'] ?? 0 }})
                    </option>
                    <option value="error" {{ request('audio_status') == 'error' ? 'selected' : '' }}>
                        Lỗi ({{ $statusCounts['error'] ?? 0 }})
                    </option>
                </select>
            </form>
        </div>

        <div class="card-body p-0">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Chương</th>
                        <th>Tiêu đề</th>
                        <th>Trạng thái</th>
                        <th>TTS Status</th>
                        <th>Audio Player</th>
                        <th>Content</th>
                        <th>Nguồn dữ liệu</th>
                        <th>Kích thước</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($chapters as $chapter)
                        <tr>
                            <td>{{ $chapter->chapter_number }}</td>
                            <td>{{ $chapter->title }}</td>
                            <td>
                                @if($chapter->is_crawled)
                                    <span class="badge badge-success">Đã crawl</span>
                                @else
                                    <span class="badge badge-secondary">Thêm thủ công</span>
                                @endif
                            </td>
                            <td>
                                {!! $chapter->tts_status_badge !!}
                                @if($chapter->hasAudio())
                                    <br><small class="text-success">
                                        <i class="fas fa-file-audio"></i>
                                        {{ basename($chapter->audio_file_path) }}
                                    </small>
                                @endif
                            </td>

                            <!-- Audio Player Column -->
                            <td>
                                @if($chapter->hasAudio())
                                    <div class="audio-player-container">
                                        <audio controls preload="none" style="width: 200px; height: 30px;">
                                            <source src="{{ $chapter->audio_url }}" type="audio/mpeg">
                                            Trình duyệt không hỗ trợ audio.
                                        </audio>
                                        <br><small class="text-muted">{{ $chapter->audio_file_name }}</small>
                                    </div>
                                @else
                                    <small class="text-muted">
                                        <i class="fas fa-volume-mute"></i> Chưa có audio
                                    </small>
                                @endif
                            </td>

                            <!-- Content Column -->
                            <td>
                                @if($chapter->hasReadableContent())
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                            data-toggle="modal"
                                            data-target="#contentModal"
                                            data-chapter-id="{{ $chapter->id }}"
                                            data-chapter-number="{{ $chapter->chapter_number }}"
                                            data-chapter-title="{{ $chapter->title }}">
                                        <i class="fas fa-eye"></i> Xem
                                    </button>
                                @else
                                    <small class="text-muted">
                                        <i class="fas fa-ban"></i> Không có nội dung
                                    </small>
                                @endif
                            </td>

                            <td>
                                @if($chapter->file_path)
                                    <small class="text-muted">
                                        <i class="fas fa-file-alt"></i>
                                        {{ basename($chapter->file_path) }}
                                        @if($chapter->hasContentInDatabase())
                                            <br><span class="badge badge-info badge-sm">DB + File</span>
                                        @else
                                            <br><span class="badge badge-warning badge-sm">Chỉ File</span>
                                        @endif
                                    </small>
                                @else
                                    <small class="text-muted">
                                        <i class="fas fa-keyboard"></i>
                                        Nhập thủ công
                                        <br><span class="badge badge-primary badge-sm">Database</span>
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($chapter->formatted_file_size)
                                    <small class="text-muted">{{ $chapter->formatted_file_size }}</small>
                                @else
                                    <small class="text-muted">-</small>
                                @endif
                            </td>
                            <td>{{ $chapter->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('chapters.edit', $chapter) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($chapter->canConvertToTts())
                                        <button type="button" class="btn btn-sm btn-success"
                                                data-toggle="modal"
                                                data-target="#ttsModal"
                                                data-chapter-id="{{ $chapter->id }}"
                                                data-chapter-number="{{ $chapter->chapter_number }}">
                                            <i class="fas fa-volume-up"></i>
                                        </button>
                                    @endif
                                    <form action="{{ route('chapters.destroy', $chapter) }}" method="POST" onsubmit="return confirm('Bạn có chắc muốn xóa chương này?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center">Chưa có chương nào</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $chapters->links('vendor.pagination.adminlte') }}
        </div>
    </div>
</div>

<!-- Modal TTS cho chapter đơn lẻ -->
<div class="modal fade" id="ttsModal" tabindex="-1" role="dialog" aria-labelledby="ttsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ttsModalLabel">Chuyển đổi Chapter thành Audio</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="ttsForm" method="POST" action="#">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="voice">Giọng đọc</label>
                        <select name="voice" id="voice" class="form-control" required>
                            <option value="hn_female_ngochuyen_full_48k-fhg">Ngọc Huyền (Nữ - Hà Nội)</option>
                            <option value="hn_male_manhtung_full_48k-fhg">Mạnh Tùng (Nam - Hà Nội)</option>
                            <option value="sg_female_thaotrinh_full_48k-fhg">Thảo Trinh (Nữ - Sài Gòn)</option>
                            <option value="sg_male_minhhoang_full_48k-fhg">Minh Hoàng (Nam - Sài Gòn)</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bitrate">Bitrate (kbps)</label>
                                <select name="bitrate" id="bitrate" class="form-control" required>
                                    <option value="64">64 kbps</option>
                                    <option value="128" selected>128 kbps</option>
                                    <option value="192">192 kbps</option>
                                    <option value="256">256 kbps</option>
                                    <option value="320">320 kbps</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="speed">Tốc độ đọc</label>
                                <select name="speed" id="speed" class="form-control" required>
                                    <option value="0.5">0.5x (Chậm)</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1.0" selected>1.0x (Bình thường)</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Nhanh)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="button" id="ttsSubmitBtn" class="btn btn-success">
                        <i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal TTS cho tất cả chapters -->
<div class="modal fade" id="ttsAllModal" tabindex="-1" role="dialog" aria-labelledby="ttsAllModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ttsAllModalLabel">Chuyển đổi tất cả Chapters thành Audio</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('chapters.tts.all', $story) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Chức năng này sẽ chuyển đổi tất cả các chapters của truyện "{{ $story->title }}" thành audio.
                    </div>

                    <div class="form-group">
                        <label for="voice_all">Giọng đọc</label>
                        <select name="voice" id="voice_all" class="form-control" required>
                            <option value="hn_female_ngochuyen_full_48k-fhg">Ngọc Huyền (Nữ - Hà Nội)</option>
                            <option value="hn_male_manhtung_full_48k-fhg">Mạnh Tùng (Nam - Hà Nội)</option>
                            <option value="sg_female_thaotrinh_full_48k-fhg">Thảo Trinh (Nữ - Sài Gòn)</option>
                            <option value="sg_male_minhhoang_full_48k-fhg">Minh Hoàng (Nam - Sài Gòn)</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bitrate_all">Bitrate (kbps)</label>
                                <select name="bitrate" id="bitrate_all" class="form-control" required>
                                    <option value="64">64 kbps</option>
                                    <option value="128" selected>128 kbps</option>
                                    <option value="192">192 kbps</option>
                                    <option value="256">256 kbps</option>
                                    <option value="320">320 kbps</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="speed_all">Tốc độ đọc</label>
                                <select name="speed" id="speed_all" class="form-control" required>
                                    <option value="0.5">0.5x (Chậm)</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1.0" selected>1.0x (Bình thường)</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Nhanh)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="only_pending" name="only_pending" value="1" checked>
                            <label class="custom-control-label" for="only_pending">
                                Chỉ xử lý chapters chưa chuyển đổi
                            </label>
                            <small class="form-text text-muted">
                                Nếu bỏ tick, tất cả chapters sẽ được xử lý lại (trừ những chapter đang xử lý)
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi tất cả
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal xem content chapter -->
<div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">Nội dung Chapter</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="contentLoading" class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Đang tải nội dung...
                </div>
                <div id="contentDisplay" style="display: none;">
                    <div class="content-text" style="max-height: 400px; overflow-y: auto; line-height: 1.6; font-size: 14px;">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
console.log('TTS Script section loaded');

$(document).ready(function() {
    console.log('Document ready - TTS script loaded');

    // Test jQuery
    console.log('jQuery version:', $.fn.jquery);

    // Test modal exists
    console.log('TTS Modal element:', $('#ttsModal').length);

    // Test buttons exist
    console.log('TTS buttons:', $('[data-toggle="modal"][data-target="#ttsModal"]').length);

    // Xử lý modal TTS cho chapter đơn lẻ
    $('#ttsModal').on('show.bs.modal', function (event) {
        console.log('Modal opening...');
        var button = $(event.relatedTarget);
        var chapterId = button.data('chapter-id');
        var chapterNumber = button.data('chapter-number');

        console.log('Button clicked:', button);
        console.log('Chapter ID:', chapterId);
        console.log('Chapter Number:', chapterNumber);

        if (!chapterId) {
            console.error('Chapter ID is missing!');
            alert('Lỗi: Không tìm thấy ID chapter');
            return;
        }

        var modal = $(this);
        var actionUrl = '{{ url("/chapters") }}/' + chapterId + '/tts';
        console.log('Setting action URL:', actionUrl);

        modal.find('.modal-title').text('Chuyển đổi Chapter ' + chapterNumber + ' thành Audio');
        modal.find('#ttsForm').attr('action', actionUrl);

        // Verify action was set
        setTimeout(function() {
            console.log('Form action after setting:', modal.find('#ttsForm').attr('action'));
        }, 100);
    });

    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Xử lý click button TTS
    $(document).on('click', '#ttsSubmitBtn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('TTS Submit button clicked');

        var form = $('#ttsForm');
        var actionUrl = form.attr('action');
        var formData = form.serialize();

        console.log('Form element:', form);
        console.log('Action URL from form:', actionUrl);
        console.log('Form data:', formData);

        // Kiểm tra action URL
        if (!actionUrl || actionUrl === '#' || actionUrl.indexOf('/tts') === -1) {
            console.error('Invalid action URL:', actionUrl);
            alert('Lỗi: URL action không hợp lệ. Vui lòng thử lại.');
            return false;
        }

        // Disable button để tránh double click
        $(this).prop('disabled', true).text('Đang xử lý...');

        $.ajax({
            url: actionUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Success:', response);
                if (response.success) {
                    alert(response.message || 'Đã bắt đầu chuyển đổi TTS cho chapter này!');
                    $('#ttsModal').modal('hide');
                    location.reload();
                } else {
                    alert('Lỗi: ' + (response.message || 'Có lỗi xảy ra'));
                }
            },
            error: function(xhr, status, error) {
                console.log('Error status:', status);
                console.log('Error:', error);
                console.log('Response:', xhr.responseText);

                var errorMessage = 'Có lỗi xảy ra';
                try {
                    var response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || errorMessage;
                } catch(e) {
                    errorMessage = error || errorMessage;
                }

                alert('Lỗi: ' + errorMessage);
            },
            complete: function() {
                // Re-enable button
                $('#ttsSubmitBtn').prop('disabled', false).html('<i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi');
            }
        });

        return false;
    });

    // Test click handler
    $(document).on('click', '[data-toggle="modal"][data-target="#ttsModal"]', function() {
        console.log('TTS button clicked!', this);
    });

    // Xử lý modal xem content
    $('#contentModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var chapterId = button.data('chapter-id');
        var chapterNumber = button.data('chapter-number');
        var chapterTitle = button.data('chapter-title');

        var modal = $(this);
        modal.find('.modal-title').text('Nội dung Chapter ' + chapterNumber + ': ' + chapterTitle);

        // Show loading
        $('#contentLoading').show();
        $('#contentDisplay').hide();

        // Load content via AJAX
        $.ajax({
            url: '{{ url("/chapters") }}/' + chapterId + '/content',
            type: 'GET',
            success: function(response) {
                $('#contentLoading').hide();
                $('#contentDisplay').show();
                $('.content-text').html('<pre style="white-space: pre-wrap; font-family: inherit;">' + response.content + '</pre>');
            },
            error: function(xhr, status, error) {
                $('#contentLoading').hide();
                $('#contentDisplay').show();
                $('.content-text').html('<div class="alert alert-danger">Lỗi khi tải nội dung: ' + error + '</div>');
            }
        });
    });
});
</script>
@endpush