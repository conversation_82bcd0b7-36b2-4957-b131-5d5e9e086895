@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Danh sách chương - {{ $story->title }}</h3>
            <div class="card-tools">
                <a href="{{ route('stories.show', $story) }}" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                <a href="{{ route('chapters.create', ['story_id' => $story->id]) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Thêm chương mới
                </a>
                <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#ttsAllModal">
                    <i class="fas fa-volume-up"></i> TTS tất cả
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Ch<PERSON>ơng</th>
                        <th>Tiêu đề</th>
                        <th>Trạng thái</th>
                        <th>TTS Status</th>
                        <th>Nguồn dữ liệu</th>
                        <th>Kích thước</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($chapters as $chapter)
                        <tr>
                            <td>{{ $chapter->chapter_number }}</td>
                            <td>{{ $chapter->title }}</td>
                            <td>
                                @if($chapter->is_crawled)
                                    <span class="badge badge-success">Đã crawl</span>
                                @else
                                    <span class="badge badge-secondary">Thêm thủ công</span>
                                @endif
                            </td>
                            <td>
                                {!! $chapter->tts_status_badge !!}
                                @if($chapter->hasAudio())
                                    <br><small class="text-success">
                                        <i class="fas fa-file-audio"></i>
                                        {{ basename($chapter->audio_file_path) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($chapter->file_path)
                                    <small class="text-muted">
                                        <i class="fas fa-file-alt"></i>
                                        {{ basename($chapter->file_path) }}
                                        @if($chapter->hasContentInDatabase())
                                            <br><span class="badge badge-info badge-sm">DB + File</span>
                                        @else
                                            <br><span class="badge badge-warning badge-sm">Chỉ File</span>
                                        @endif
                                    </small>
                                @else
                                    <small class="text-muted">
                                        <i class="fas fa-keyboard"></i>
                                        Nhập thủ công
                                        <br><span class="badge badge-primary badge-sm">Database</span>
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($chapter->formatted_file_size)
                                    <small class="text-muted">{{ $chapter->formatted_file_size }}</small>
                                @else
                                    <small class="text-muted">-</small>
                                @endif
                            </td>
                            <td>{{ $chapter->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('chapters.edit', $chapter) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($chapter->canConvertToTts())
                                        <button type="button" class="btn btn-sm btn-success"
                                                data-toggle="modal"
                                                data-target="#ttsModal"
                                                data-chapter-id="{{ $chapter->id }}"
                                                data-chapter-number="{{ $chapter->chapter_number }}">
                                            <i class="fas fa-volume-up"></i>
                                        </button>
                                    @endif
                                    <form action="{{ route('chapters.destroy', $chapter) }}" method="POST" onsubmit="return confirm('Bạn có chắc muốn xóa chương này?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center">Chưa có chương nào</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $chapters->links('vendor.pagination.adminlte') }}
        </div>
    </div>
</div>

<!-- Modal TTS cho chapter đơn lẻ -->
<div class="modal fade" id="ttsModal" tabindex="-1" role="dialog" aria-labelledby="ttsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ttsModalLabel">Chuyển đổi Chapter thành Audio</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="ttsForm" method="POST" action="#">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="voice">Giọng đọc</label>
                        <select name="voice" id="voice" class="form-control" required>
                            <option value="hn_female_ngochuyen_full_48k-fhg">Ngọc Huyền (Nữ - Hà Nội)</option>
                            <option value="hn_male_manhtung_full_48k-fhg">Mạnh Tùng (Nam - Hà Nội)</option>
                            <option value="sg_female_thaotrinh_full_48k-fhg">Thảo Trinh (Nữ - Sài Gòn)</option>
                            <option value="sg_male_minhhoang_full_48k-fhg">Minh Hoàng (Nam - Sài Gòn)</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bitrate">Bitrate (kbps)</label>
                                <select name="bitrate" id="bitrate" class="form-control" required>
                                    <option value="64">64 kbps</option>
                                    <option value="128" selected>128 kbps</option>
                                    <option value="192">192 kbps</option>
                                    <option value="256">256 kbps</option>
                                    <option value="320">320 kbps</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="speed">Tốc độ đọc</label>
                                <select name="speed" id="speed" class="form-control" required>
                                    <option value="0.5">0.5x (Chậm)</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1.0" selected>1.0x (Bình thường)</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Nhanh)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="button" id="ttsSubmitBtn" class="btn btn-success">
                        <i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal TTS cho tất cả chapters -->
<div class="modal fade" id="ttsAllModal" tabindex="-1" role="dialog" aria-labelledby="ttsAllModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ttsAllModalLabel">Chuyển đổi tất cả Chapters thành Audio</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('chapters.tts.all', $story) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Chức năng này sẽ chuyển đổi tất cả các chapters của truyện "{{ $story->title }}" thành audio.
                    </div>

                    <div class="form-group">
                        <label for="voice_all">Giọng đọc</label>
                        <select name="voice" id="voice_all" class="form-control" required>
                            <option value="hn_female_ngochuyen_full_48k-fhg">Ngọc Huyền (Nữ - Hà Nội)</option>
                            <option value="hn_male_manhtung_full_48k-fhg">Mạnh Tùng (Nam - Hà Nội)</option>
                            <option value="sg_female_thaotrinh_full_48k-fhg">Thảo Trinh (Nữ - Sài Gòn)</option>
                            <option value="sg_male_minhhoang_full_48k-fhg">Minh Hoàng (Nam - Sài Gòn)</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bitrate_all">Bitrate (kbps)</label>
                                <select name="bitrate" id="bitrate_all" class="form-control" required>
                                    <option value="64">64 kbps</option>
                                    <option value="128" selected>128 kbps</option>
                                    <option value="192">192 kbps</option>
                                    <option value="256">256 kbps</option>
                                    <option value="320">320 kbps</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="speed_all">Tốc độ đọc</label>
                                <select name="speed" id="speed_all" class="form-control" required>
                                    <option value="0.5">0.5x (Chậm)</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1.0" selected>1.0x (Bình thường)</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2.0">2.0x (Nhanh)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="only_pending" name="only_pending" value="1" checked>
                            <label class="custom-control-label" for="only_pending">
                                Chỉ xử lý chapters chưa chuyển đổi
                            </label>
                            <small class="form-text text-muted">
                                Nếu bỏ tick, tất cả chapters sẽ được xử lý lại (trừ những chapter đang xử lý)
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi tất cả
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('Document ready - TTS script loaded');

    // Xử lý modal TTS cho chapter đơn lẻ
    $('#ttsModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var chapterId = button.data('chapter-id');
        var chapterNumber = button.data('chapter-number');

        console.log('Button clicked:', button);
        console.log('Chapter ID:', chapterId);
        console.log('Chapter Number:', chapterNumber);

        if (!chapterId) {
            console.error('Chapter ID is missing!');
            return;
        }

        var modal = $(this);
        var actionUrl = '{{ url("/chapters") }}/' + chapterId + '/tts';
        console.log('Action URL:', actionUrl);

        modal.find('.modal-title').text('Chuyển đổi Chapter ' + chapterNumber + ' thành Audio');
        modal.find('#ttsForm').attr('action', actionUrl);

        // Verify action was set
        console.log('Form action after setting:', modal.find('#ttsForm').attr('action'));
    });

    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Xử lý click button TTS
    $(document).on('click', '#ttsSubmitBtn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('TTS Submit button clicked');

        var form = $('#ttsForm');
        var actionUrl = form.attr('action');
        var formData = form.serialize();

        console.log('Form element:', form);
        console.log('Action URL from form:', actionUrl);
        console.log('Form data:', formData);

        // Kiểm tra action URL
        if (!actionUrl || actionUrl === '#' || actionUrl.indexOf('/tts') === -1) {
            console.error('Invalid action URL:', actionUrl);
            alert('Lỗi: URL action không hợp lệ. Vui lòng thử lại.');
            return false;
        }

        // Disable button để tránh double click
        $(this).prop('disabled', true).text('Đang xử lý...');

        $.ajax({
            url: actionUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                console.log('Success:', response);
                if (response.success) {
                    alert(response.message || 'Đã bắt đầu chuyển đổi TTS cho chapter này!');
                    $('#ttsModal').modal('hide');
                    location.reload();
                } else {
                    alert('Lỗi: ' + (response.message || 'Có lỗi xảy ra'));
                }
            },
            error: function(xhr, status, error) {
                console.log('Error status:', status);
                console.log('Error:', error);
                console.log('Response:', xhr.responseText);

                var errorMessage = 'Có lỗi xảy ra';
                try {
                    var response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || errorMessage;
                } catch(e) {
                    errorMessage = error || errorMessage;
                }

                alert('Lỗi: ' + errorMessage);
            },
            complete: function() {
                // Re-enable button
                $('#ttsSubmitBtn').prop('disabled', false).html('<i class="fas fa-volume-up"></i> Bắt đầu chuyển đổi');
            }
        });

        return false;
    });
});
</script>
@endsection