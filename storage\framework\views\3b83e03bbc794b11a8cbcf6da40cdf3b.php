<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Danh sách chương - <?php echo e($story->title); ?></h3>
            <div class="card-tools">
                <a href="<?php echo e(route('stories.show', $story)); ?>" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                <a href="<?php echo e(route('chapters.create', ['story_id' => $story->id])); ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Thêm chương mới
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th><PERSON><PERSON><PERSON><PERSON></th>
                        <th>Tiêu đề</th>
                        <th>Tr<PERSON><PERSON> thái</th>
                        <th>Nguồn dữ liệu</th>
                        <th><PERSON><PERSON><PERSON> thước</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $chapters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($chapter->chapter_number); ?></td>
                            <td><?php echo e($chapter->title); ?></td>
                            <td>
                                <?php if($chapter->is_crawled): ?>
                                    <span class="badge badge-success">Đã crawl</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">Thêm thủ công</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($chapter->file_path): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-file-alt"></i>
                                        <?php echo e(basename($chapter->file_path)); ?>

                                        <?php if($chapter->hasContentInDatabase()): ?>
                                            <br><span class="badge badge-info badge-sm">DB + File</span>
                                        <?php else: ?>
                                            <br><span class="badge badge-warning badge-sm">Chỉ File</span>
                                        <?php endif; ?>
                                    </small>
                                <?php else: ?>
                                    <small class="text-muted">
                                        <i class="fas fa-keyboard"></i>
                                        Nhập thủ công
                                        <br><span class="badge badge-primary badge-sm">Database</span>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($chapter->formatted_file_size): ?>
                                    <small class="text-muted"><?php echo e($chapter->formatted_file_size); ?></small>
                                <?php else: ?>
                                    <small class="text-muted">-</small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($chapter->created_at->format('d/m/Y H:i')); ?></td>
                            <td>
                                <div class="btn-group">
                                    <a href="<?php echo e(route('chapters.edit', $chapter)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('chapters.destroy', $chapter)); ?>" method="POST" onsubmit="return confirm('Bạn có chắc muốn xóa chương này?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="text-center">Chưa có chương nào</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            <?php echo e($chapters->links('vendor.pagination.adminlte')); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\audio-lara\resources\views/stories/chapters.blade.php ENDPATH**/ ?>