

<?php $__env->startSection('content'); ?>
<div class="container">
    <h2>📚 <PERSON>h sách truyện</h2>
    <a href="<?php echo e(route('stories.create')); ?>" class="btn btn-primary mb-3">+ Thêm truyện</a>

    <?php if(session('success')): ?>
        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
    <?php endif; ?>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Tiêu đề</th>
                <th>Slug</th>
                <th>Chương</th>
                <th>Trạng thái</th>
                <th>Hành động</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $stories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($story->title); ?></td>
                <td><?php echo e($story->slug); ?></td>
                <td><?php echo e($story->start_chapter); ?> → <?php echo e($story->end_chapter); ?></td>
                <td>
                    <?php if($story->crawl_status == 0): ?> <span class="text-secondary">Chưa crawl</span>
                    <?php elseif($story->crawl_status == 1): ?> <span class="text-success">Đã crawl</span>
                    <?php else: ?> <span class="text-warning">Cần crawl lại</span> <?php endif; ?>
                </td>
                <td>
                    <div class="btn-group">
                        <a href="<?php echo e(route('stories.show', $story)); ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="<?php echo e(route('stories.edit', $story)); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('stories.destroy', $story)); ?>" method="POST" onsubmit="return confirm('Bạn có chắc muốn xóa truyện này?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <?php echo e($stories->links()); ?>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\audio-lara\resources\views/stories/index.blade.php ENDPATH**/ ?>