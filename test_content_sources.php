<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TEST CONTENT SOURCES ===\n\n";

// Test 1: Chapter có content trong database
$dbChapter = App\Models\Chapter::where('content', '!=', '')->first();
if ($dbChapter) {
    echo "1. Chapter có content trong DB:\n";
    echo "   ID: {$dbChapter->id}\n";
    echo "   Chapter: {$dbChapter->chapter_number}\n";
    echo "   Has content in DB: " . ($dbChapter->hasContentInDatabase() ? 'Yes' : 'No') . "\n";
    echo "   Has readable content: " . ($dbChapter->hasReadableContent() ? 'Yes' : 'No') . "\n";
    echo "   Content length: " . strlen($dbChapter->content) . " chars\n";
    echo "   Can convert TTS: " . ($dbChapter->canConvertToTts() ? 'Yes' : 'No') . "\n\n";
}

// Test 2: Chapter chỉ có file_path
$fileChapter = App\Models\Chapter::whereNull('content')
    ->orWhere('content', '')
    ->whereNotNull('file_path')
    ->first();
    
if ($fileChapter) {
    echo "2. Chapter chỉ có file_path:\n";
    echo "   ID: {$fileChapter->id}\n";
    echo "   Chapter: {$fileChapter->chapter_number}\n";
    echo "   File path: {$fileChapter->file_path}\n";
    echo "   File exists: " . (file_exists($fileChapter->file_path) ? 'Yes' : 'No') . "\n";
    echo "   Has content in DB: " . ($fileChapter->hasContentInDatabase() ? 'Yes' : 'No') . "\n";
    echo "   Has readable content: " . ($fileChapter->hasReadableContent() ? 'Yes' : 'No') . "\n";
    if ($fileChapter->hasReadableContent()) {
        echo "   Content length: " . strlen($fileChapter->content) . " chars\n";
    }
    echo "   Can convert TTS: " . ($fileChapter->canConvertToTts() ? 'Yes' : 'No') . "\n\n";
}

// Test 3: Chapter không có gì
$emptyChapter = App\Models\Chapter::where(function($q) {
    $q->whereNull('content')->orWhere('content', '');
})->where(function($q) {
    $q->whereNull('file_path')->orWhere('file_path', '');
})->first();

if ($emptyChapter) {
    echo "3. Chapter không có content:\n";
    echo "   ID: {$emptyChapter->id}\n";
    echo "   Chapter: {$emptyChapter->chapter_number}\n";
    echo "   Has content in DB: " . ($emptyChapter->hasContentInDatabase() ? 'Yes' : 'No') . "\n";
    echo "   Has readable content: " . ($emptyChapter->hasReadableContent() ? 'Yes' : 'No') . "\n";
    echo "   Can convert TTS: " . ($emptyChapter->canConvertToTts() ? 'Yes' : 'No') . "\n\n";
}

echo "=== TEST COMPLETED ===\n";
